# Dima AI agent

# Implementation notes

Heavily copied/inspired by <PERSON><PERSON>'s Rovo Dev.

The goal is to have free providers only, but since the API keys are read from environment variables,
it is trivial to switch to paid providers.

This probably provides worse results because Rovo Dev supposedly uses Claude Sonnet 4, 3.7 or 3.5v2 which are tuned to
agentic workflows, and are the best for such workflows.

## Differences to Rovo Dev

- no streaming support implemented and likely not planned, I just don't really care about it
- different system prompts, but based on theirs
- no `expand_code_chunks` tool, I suppose RovoDev uses tree-sitter to construct it, a bit like `aider`
- `create_file` tool is renamed to `write_file` because I notice that many models mess up the `find_and_replace_code`
  tool, to force them to use it. And the `find_and_replace_code` tool is optional
- different Markdown rendering to avoid the pretty borders for quicker copying out of terminal
- my `grep_file_content` tool uses `ripgrep` to respect `.gitignore` and is always in `--ignore-case` mode. I have not
  verified how RovoDev does it

# Setup

Clone this and then run `cargo install --path .`

The binary will be named `dima-ai-agent-v2`.

## Help

Run `dima-ai-agent-v2 --help` and run it, then you enter in `.help` or just enter in `.` which shows all available
commands.

## ripgrep (the `rg` binary)

https://github.com/BurntSushi/ripgrep

It is used because it respects `.gitignore`, and is pretty fast.
It is used with `--ignore-case`, so note that for huge code-bases.

It is required for the `grep_file_content` tool.
And for constructing a list of files in the working directories into the system prompt, named workspace.

## yek

https://github.com/bodo-run/yek

To copy all files in the working directory into the system prompt, the `yek` command is used.

# Usage

## Edit tools

By default, `OnlyWriteFile` is used which can be overridden with `--edit-tool`.
This leads to slower edits of files, but far more reliable results because weaker models are not capable of using the
`find_and_replace_code` tool correctly most of the time. Even stronger models like Gemini 2.5 Pro mess it up frequently.

Note that with `OnlyWriteFile`, you should be careful to not have it edit huge files, as it would blow the output token
count of the model.

# Free API key providers

- https://github.com/cheahjs/free-llm-api-resources
- https://github.com/zukixa/cool-ai-stuff
- https://pollinations.ai/ (see https://text.pollinations.ai/models) - this does not even need an API key

# Free agents

## Augment Code

https://www.augmentcode.com/pricing

Provides 50 free agent interactions per month.

It is excellent. Its context engine is also one of the RAG solutions out there.

## Trae

https://www.trae.ai/

But the rate limits hit hard, so it is not so much fun to use.

## Relevance AI

https://relevanceai.com/pricing

Untested, has 100 free credits per day.

# Notes

Read `MODEL_NOTES.md` which contains info on my test runs with the models.
Note that I previously tested the models with the `find_and_replace_code` tool, which might explain their bad
performance.

## Model recommendations

- Gemini 2.5 Flash
- Gemini 2.0 Flash
- Codestral (a bit weak)

## About ripgrep

Because `rg` is used, you can include a `.rgignore` file in the root directory to ignore certain directories.
This is done in this repository for the `llm/` submodule.