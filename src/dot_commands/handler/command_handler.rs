use crate::display::print::print_error;
use crate::dot_commands::all_command::handle_all_command;
use crate::dot_commands::clear_command::handle_clear_command;
use crate::dot_commands::cwd_command::handle_cwd_command;
use crate::dot_commands::diff_command::handle_diff_command;
use crate::dot_commands::help_command::print_help;
use crate::dot_commands::info_command::handle_info_command;
use crate::dot_commands::model_command::open_provider_picker;
use crate::dot_commands::name_command::handle_name_command;
use crate::dot_commands::relogin_command::handle_relogin_command;
use crate::dot_commands::retry_command::handle_retry_command;
use crate::dot_commands::session_command::handle_session_command;
use crate::dot_commands::summary_command::handle_summary_command;
use crate::dot_commands::workspace_command::handle_workspace_command;
use crate::dot_commands::yek_command::execute_yek_command;
use crate::App;
use enum_iterator::Sequence;
use std::sync::{Arc, Mutex};

#[derive(Debug, PartialEq, Sequence)]
pub enum DotCommand {
    CopySessionId,
    CopyLastResponse,
    CopyAllResponses,
    CopyWorkspace,
    ChangeProvider,
    ClearSession,
    PrintHelp,
    Exit,
    Retry,
    Yek,
    Cwd,
    Info,
    Diff,
    Name,
    Summary,
    Relogin,
}

pub enum DotCommandResult {
    Exit,
    Continue,
    Retry,
}

pub struct CommandContext<'a> {
    pub input: &'a str,
    pub session_id: &'a str,
    pub app_arc: &'a Arc<Mutex<App>>,
}

impl DotCommand {
    pub fn description(&self) -> &'static str {
        match self {
            DotCommand::CopySessionId => "Copy session ID to clipboard",
            DotCommand::CopyLastResponse => "Copy last AI response to clipboard",
            DotCommand::CopyAllResponses => "Copy all conversation history to clipboard",
            DotCommand::CopyWorkspace => "Copy workspace info to clipboard",
            DotCommand::ChangeProvider => "Show current model or change to specified model",
            DotCommand::PrintHelp => "Show available commands",
            DotCommand::Exit => "Exit the program",
            DotCommand::ClearSession => "Clear the session",
            DotCommand::Retry => "Retry the last request",
            DotCommand::Yek => "Execute yek | cat and append to conversation",
            DotCommand::Cwd => "Show current working directory",
            DotCommand::Info => "Show information about the current session and model",
            DotCommand::Diff => "Run git diff --stat HEAD with colors and prints it",
            DotCommand::Name => "Name the current session",
            DotCommand::Summary => {
                "Print the last 20 messages (user and assistant), truncated to 100 chars"
            }
            DotCommand::Relogin => "Re-authenticate with GitHub Copilot",
        }
    }

    pub fn name(&self) -> &'static str {
        match self {
            DotCommand::CopySessionId => ".session",
            DotCommand::Yek => ".yek",
            DotCommand::CopyLastResponse => ".last",
            DotCommand::CopyAllResponses => ".all",
            DotCommand::CopyWorkspace => ".workspace",
            DotCommand::ChangeProvider => ".model",
            DotCommand::PrintHelp => ".help",
            DotCommand::Exit => ".exit",
            DotCommand::ClearSession => ".clear",
            DotCommand::Retry => ".retry",
            DotCommand::Cwd => ".cwd",
            DotCommand::Info => ".info",
            DotCommand::Diff => ".diff",
            DotCommand::Name => ".name",
            DotCommand::Summary => ".summary",
            DotCommand::Relogin => ".relogin",
        }
    }

    pub fn execute(&self, ctx: CommandContext) -> DotCommandResult {
        match self {
            DotCommand::CopySessionId => {
                handle_session_command(ctx.session_id);
                DotCommandResult::Continue
            }
            DotCommand::CopyLastResponse => {
                print_error("TODO".into());
                DotCommandResult::Continue
            }
            DotCommand::CopyAllResponses => {
                handle_all_command(ctx.app_arc);
                DotCommandResult::Continue
            }
            DotCommand::CopyWorkspace => {
                handle_workspace_command(ctx.app_arc);
                DotCommandResult::Continue
            }
            DotCommand::ChangeProvider => {
                open_provider_picker(ctx);
                DotCommandResult::Continue
            }
            DotCommand::PrintHelp => {
                print_help();
                DotCommandResult::Continue
            }
            DotCommand::ClearSession => {
                handle_clear_command(ctx.app_arc);
                DotCommandResult::Continue
            }
            DotCommand::Exit => DotCommandResult::Exit,
            DotCommand::Retry => handle_retry_command(ctx.app_arc),
            DotCommand::Yek => {
                execute_yek_command(ctx);
                DotCommandResult::Continue
            }
            DotCommand::Cwd => {
                handle_cwd_command(ctx.app_arc);
                DotCommandResult::Continue
            }
            DotCommand::Info => {
                handle_info_command(ctx.app_arc);
                DotCommandResult::Continue
            }
            DotCommand::Diff => {
                handle_diff_command(ctx.app_arc);
                DotCommandResult::Continue
            }
            DotCommand::Name => {
                handle_name_command(ctx);
                DotCommandResult::Continue
            }
            DotCommand::Summary => {
                handle_summary_command(ctx.app_arc);
                DotCommandResult::Continue
            }
            DotCommand::Relogin => {
                handle_relogin_command(ctx.app_arc);
                DotCommandResult::Continue
            }
        }
    }
}

/// Returns None if the input is not a dot command
pub fn parse_input_into_command(input: &str) -> Option<DotCommand> {
    let (command, _args) = match input.split_once(' ') {
        Some((command, args)) => (command, Some(args)),
        None => (input, None),
    };

    match command {
        ".session" => Some(DotCommand::CopySessionId),
        ".last" => Some(DotCommand::CopyLastResponse),
        ".all" => Some(DotCommand::CopyAllResponses),
        ".workspace" => Some(DotCommand::CopyWorkspace),
        ".model" => Some(DotCommand::ChangeProvider),
        ".help" => Some(DotCommand::PrintHelp),
        ".exit" => Some(DotCommand::Exit),
        ".retry" => Some(DotCommand::Retry),
        ".clear" => Some(DotCommand::ClearSession),
        ".yek" => Some(DotCommand::Yek),
        ".cwd" => Some(DotCommand::Cwd),
        ".info" => Some(DotCommand::Info),
        ".diff" => Some(DotCommand::Diff),
        ".name" => Some(DotCommand::Name),
        ".summary" => Some(DotCommand::Summary),
        ".relogin" => Some(DotCommand::Relogin),
        _ => None,
    }
}
