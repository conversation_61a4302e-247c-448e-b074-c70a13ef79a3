use crate::display::print::{print_command, print_error};
use crate::dot_commands::handler::command_handler::CommandContext;
use crate::session_management::session_manager;
use crossterm::{execute, terminal::SetTitle};
use inquire::Text;
use std::fs;

pub fn handle_name_command(ctx: CommandContext) {
    let app_arc = ctx.app_arc.clone();
    let mut app = app_arc.lock().unwrap();

    let session_name = if let Some((_, name_arg)) = ctx.input.split_once(' ') {
        name_arg.to_string()
    } else {
        match Text::new("Enter session name:").prompt() {
            Ok(name) => name,
            Err(_) => {
                print_error("Failed to read session name.".to_string());
                return;
            }
        }
    };

    let old_session_id = app.session_id.clone();
    let new_session_id =
        session_manager::generate_session_id_with_name(&app.working_dir, &session_name);

    // Ensure the current session is saved before renaming
    if let Err(e) = session_manager::save_session(&app) {
        print_error(format!(
            "Failed to save current session before renaming: {e}"
        ));
        return;
    }

    let old_path = session_manager::get_session_dir().join(&old_session_id);
    let new_path = session_manager::get_session_dir().join(&new_session_id);

    match fs::rename(&old_path, &new_path) {
        Ok(_) => {
            app.session_id = new_session_id;
            print_command(format!("Session renamed to '{session_name}'"));

            // Update terminal title
            let working_dir_name = app
                .working_dir
                .file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("unknown");
            let title = format!("Dima AI - {}", working_dir_name);
            if let Err(e) = execute!(std::io::stdout(), SetTitle(title)) {
                eprintln!("Error setting terminal title: {}", e);
            }
        }
        Err(e) => {
            print_error(format!("Failed to rename session directory: {e}"));
        }
    }
}
