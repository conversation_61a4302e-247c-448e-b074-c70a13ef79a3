use crate::display::print::{print_command, print_error};
use crate::App;
use std::sync::{Arc, Mutex};

pub fn handle_relogin_command(app_arc: &Arc<Mutex<App>>) {
    let app = app_arc.lock().unwrap();
    
    // Check if the current model is GitHub Copilot
    if !app.current_model().get_config().is_github_copilot {
        print_error("The current model is not GitHub Copilot. Relogin is only available for GitHub Copilot models.".to_string());
        return;
    }
    
    print_command("Initiating GitHub Copilot re-authentication...".to_string());
    
    // Call the relogin method on the LLM provider
    match app.llm.relogin_github_copilot() {
        Ok(()) => {
            print_command("GitHub Copilot re-authentication completed successfully!".to_string());
        }
        Err(e) => {
            print_error(format!("GitHub Copilot re-authentication failed: {}", e));
        }
    }
}
