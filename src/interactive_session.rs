use crate::api_handler::{call_api, ApiCallResult};
use crate::app::{App, <PERSON><PERSON><PERSON><PERSON><PERSON>, System<PERSON>rompt};
use crate::automatic_linter::run_lint_command_if_needed;
use crate::bash_exclamation::handle_bash_command;
use crate::cli::cli_args::Cli;
use crate::display::print::{print_command, print_error};
use crate::display::startup_banner::print_startup_banner;
use crate::dot_commands::commands_command::handle_commands_command;
use crate::dot_commands::handler::command_handler::{
    parse_input_into_command, CommandContext, DotCommandResult,
};
use crate::dot_commands::model_command::open_provider_picker;
use crate::dot_commands::retry_command::handle_retry_command;
use crate::notifications::show_user_input_required_notification;
use crate::provider_models::llm_provider_builder::build_llm_provider;
use crate::reedline::suspendable_reedline::{ReadResult, SuspendableReedline};
use crate::reedline::token_hinter::CurrentPromptBufferState;
use crate::session_management::global_state::{self, load_global_state, GlobalAppState};
use crate::session_management::session_manager::{self, Session};
use crate::utils::git::find_git_root_or_cwd;
use crate::utils::system_prompt::create_system_prompt;
use crate::workspace::workspace_for_system_prompt::create_workspace_for_system_prompt;
use crate::{constants::MAX_API_RETRY_FAILURE, user_agent_config};
use clap::Parser;
use crossterm::{execute, terminal::SetTitle};
use llm::chat::ChatMessage;
use std::env::temp_dir;
use std::error::Error;
use std::io::IsTerminal;
use std::process::Command;
use std::sync::{Arc, Mutex};
use user_agent_config::load_dima_agent_config;

/// Encapsulates the application setup logic.
/// This function parses CLI arguments, loads configuration, sets up the LLM,
/// and restores a session if requested.
struct RestoreInfo {
    session_id: String,
    message_count: usize,
}

async fn setup_app(
    cli_args: Cli,
) -> Result<(Arc<Mutex<App>>, SystemPrompt, Option<RestoreInfo>), Box<dyn Error>> {
    let cwd = find_git_root_or_cwd();
    let proxy_url = cli_args
        .proxy_localhost
        .map(|port| format!("http://localhost:{port}"));

    // Determine the current model, preferring global state over CLI default
    let mut current_model = cli_args.llm_provider;
    if let Ok(Some(global_state)) = load_global_state() {
        current_model = global_state.current_llm_provider;
    } else {
        let new_state = GlobalAppState::new(current_model);
        global_state::save_global_state(&new_state)?;
    }

    // Create system prompt and LLM provider
    let system_prompt = create_system_prompt(cwd.clone(), &cli_args);
    let llm = build_llm_provider(
        system_prompt.clone().content,
        proxy_url,
        current_model.get_config(),
        &cli_args,
    );

    // Handle session restoring logic
    let mut session_to_load: Option<Session> = None;
    let mut restore_info: Option<RestoreInfo> = None;

    let session_id = if let Some(restore_arg) = &cli_args.restore {
        if restore_arg.is_empty() {
            match session_manager::find_latest_session_for_working_dir(&cwd) {
                Ok(Some(latest_id)) => match session_manager::load_session(&latest_id) {
                    Ok(session) => {
                        let message_count = session.messages.len();
                        restore_info = Some(RestoreInfo {
                            session_id: latest_id.clone(),
                            message_count,
                        });
                        session_to_load = Some(session);
                        latest_id
                    }
                    Err(e) => {
                        print_error(format!("Failed to load session {latest_id}: {e}"));
                        session_manager::generate_session_id(&cwd)
                    }
                },
                Ok(None) => {
                    print_command(
                        "No previous session found for this directory. Starting a new session.",
                    );
                    session_manager::generate_session_id(&cwd)
                }
                Err(e) => {
                    print_error(format!("Error finding latest session: {e}"));
                    session_manager::generate_session_id(&cwd)
                }
            }
        } else {
            print_command(format!("Restoring session: {restore_arg}"));
            match session_manager::load_session(restore_arg) {
                Ok(session) => {
                    let message_count = session.messages.len();
                    restore_info = Some(RestoreInfo {
                        session_id: restore_arg.clone(),
                        message_count,
                    });
                    session_to_load = Some(session);
                    restore_arg.clone()
                }
                Err(e) => {
                    print_error(format!("Failed to load session {restore_arg}: {e}"));
                    session_manager::generate_session_id(&cwd)
                }
            }
        }
    } else {
        session_manager::generate_session_id(&cwd)
    };

    let dima_config = load_dima_agent_config(&cwd);

    // Build the App instance using the builder
    let mut app = AppBuilder::new()
        .working_dir(cwd.clone())
        .cli_args(cli_args)
        .system_prompt(system_prompt.clone())
        .llm(llm)
        .current_model(current_model)
        .session_id(session_id)
        .dima_config(dima_config)
        .build()?;

    // If a session was loaded, apply its state to the App instance
    if let Some(loaded_session) = session_to_load {
        app.set_conversation_messages(loaded_session.messages);
        app.set_last_response_usage_tokens(loaded_session.metadata.last_response_usage_tokens);
        app.session_allowed_bash_commands = loaded_session.metadata.allowed_bash_commands;
        app.session_denied_bash_commands = loaded_session.metadata.denied_bash_commands;
    }

    // Set terminal title
    let working_dir_name = cwd
        .file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("unknown");
    let title = format!("Dima AI - {}", working_dir_name);
    if let Err(e) = execute!(std::io::stdout(), SetTitle(title)) {
        eprintln!("Error setting terminal title: {}", e);
    }

    Ok((Arc::new(Mutex::new(app)), system_prompt, restore_info))
}

pub async fn start() -> Result<(), Box<dyn Error>> {
    let cli_args = Cli::parse();
    let (app_arc, system_prompt, restore_info) = setup_app(cli_args.clone()).await?;
    let is_interactive = std::io::stdin().is_terminal();

    // Handle --debug-workspace as a special case that exits early
    if cli_args.debug_workspace {
        let cwd = find_git_root_or_cwd();
        return match create_workspace_for_system_prompt(cwd, cli_args.workspace_token_limit) {
            Ok(workspace) => {
                println!("{workspace}");
                Ok(())
            }
            Err(e) => Err(e),
        };
    }

    if !is_interactive {
        if cli_args.initial_message.is_none() {
            eprintln!("In non-interactive mode, --initial-message is required.");
            std::process::exit(1);
        } else {
            // If it's not interactive and an initial message is provided, process it and exit.
            let initial_message = cli_args.initial_message.clone().unwrap();
            app_arc
                .lock()
                .unwrap()
                .add_message(ChatMessage::user().content(initial_message.clone()).build());
            // We need a retry mechanism here as well.
            handle_api_call_with_retries(&app_arc.clone()).await;
            return Ok(());
        }
    }

    // Extract necessary info for banner and loop after app is created
    let (working_dir, session_id, model_type) = {
        let app = app_arc.lock().unwrap();
        (
            app.working_dir.clone(),
            app.session_id.clone(),
            app.current_model(),
        )
    };

    print_startup_banner(
        &working_dir,
        cli_args.proxy_localhost,
        model_type,
        system_prompt.with_custom_user_prompt,
        restore_info.map(|info| {
            format!(
                "Restored {} messages from session: {}",
                info.message_count, info.session_id
            )
        }),
    );

    // Create a suspendable editor with external editor support and custom hinter
    let temp_file = temp_dir().join("rust_llm_tui_edit_buffer.tmp");
    let editor_cmd_str = std::env::var("EDITOR").unwrap_or_else(|_| "emacsclient".to_string());
    let mut editor_os_cmd = Command::new(editor_cmd_str);
    editor_os_cmd.arg(&temp_file);

    let prompt_buffer_state_arc = Arc::new(Mutex::new(CurrentPromptBufferState::default()));

    /// Contains the retry loop for API calls.
    async fn handle_api_call_with_retries(app_arc: &Arc<Mutex<App>>) -> ApiCallResult {
        let max_retries = MAX_API_RETRY_FAILURE;
        for i in 0..max_retries {
            // call_api is the one that has the `select!` for ctrl+c
            let result = call_api(app_arc, i).await;

            // call_api returns ContinueWithUserInteraction on Ctrl+C during sleep
            // and Retry on successful sleep.
            if let ApiCallResult::Retry(_) = result {
                continue; // Loop to retry
            } else {
                return result; // Return on success, tool fail, or user abort
            }
        }
        print_error("Maximum retries reached. Aborting.".to_string());
        ApiCallResult::ContinueWithUserInteraction
    }

    let mut line_editor = SuspendableReedline::create_with_history_and_hinter(
        app_arc.clone(),
        prompt_buffer_state_arc.clone(),
    )
    .with_buffer_editor(editor_os_cmd, temp_file);

    let mut notification_required_on_user_input = false;
    let mut call_api_without_user_interaction = cli_args.initial_message.is_some();

    // Handle initial commands from CLI args
    if cli_args.yek {
        if let Some(command) = parse_input_into_command(".yek") {
            command.execute(CommandContext {
                input: ".yek",
                session_id: &session_id,
                app_arc: &app_arc,
            });
        } else {
            print_error("Failed to parse .yek command.".to_string());
        }
    }
    if let Some(initial_message) = &cli_args.initial_message {
        println!("> {initial_message}");
        app_arc
            .lock()
            .unwrap()
            .add_message(ChatMessage::user().content(initial_message.clone()).build());
    }

    'main_loop: loop {
        let is_aborted;
        {
            let mut app = app_arc.lock().unwrap();
            is_aborted = app.is_aborted_by_ctrl_c();
            if is_aborted {
                app.set_is_aborted_by_ctrl_c(false);
                call_api_without_user_interaction = false; // Ensure human-turn after abort
            }
        } // `app` (MutexGuard) is dropped here

        if !call_api_without_user_interaction && !is_aborted {
            let lint_result = run_lint_command_if_needed(&app_arc).await;
            if let Some(lint_output) = lint_result {
                // If linting failed, add the output to the conversation and let the AI respond
                app_arc
                    .lock()
                    .unwrap()
                    .add_message(ChatMessage::user().content(lint_output).build());
                call_api_without_user_interaction = true;
            }
        }

        // If it's a human turn and a notification is due
        if !call_api_without_user_interaction && notification_required_on_user_input {
            show_user_input_required_notification(&cli_args);
            notification_required_on_user_input = false;
        }
        if call_api_without_user_interaction {
            let api_call_result = handle_api_call_with_retries(&app_arc.clone()).await;
            // The result is now final (not a retry). We just need to know if we should
            // continue the machine-turn or switch to a human-turn.
            call_api_without_user_interaction = matches!(
                api_call_result,
                ApiCallResult::ContinueWithoutUserInteraction | ApiCallResult::ToolFailed
            );
            // If API call just finished and it's now human's turn, set flag for next iteration
            if !call_api_without_user_interaction {
                notification_required_on_user_input = true;
            }
        } else {
            let read_result = line_editor.read_line();
            println!();
            if let Ok(result) = read_result {
                match result {
                    ReadResult::DoNothing => {}
                    ReadResult::Success(success) => {
                        let trimmed_buffer = success.trim();
                        if trimmed_buffer.is_empty() {
                            // If the buffer is empty, do nothing and continue the loop
                        } else if let Some(command_to_execute) = trimmed_buffer.strip_prefix('!') {
                            let command_to_execute = command_to_execute.trim();
                            if command_to_execute.is_empty() {
                                print_error("Empty bash command.".to_string());
                            } else {
                                handle_bash_command(&app_arc, command_to_execute).await;
                            }
                        } else if trimmed_buffer.starts_with('.') {
                            match parse_input_into_command(trimmed_buffer) {
                                None => {
                                    print_error(format!(
                                        "Unknown command: {trimmed_buffer}, hit tab after typing dot to see all commands"
                                    ));
                                }
                                Some(command) => {
                                    notification_required_on_user_input = false;
                                    let command_result = command.execute(CommandContext {
                                        input: trimmed_buffer,
                                        session_id: &session_id,
                                        app_arc: &app_arc,
                                    });
                                    match command_result {
                                        DotCommandResult::Exit => break 'main_loop,
                                        DotCommandResult::Continue => {
                                            /* Do nothing, continue loop */
                                            notification_required_on_user_input = true;
                                        }
                                        DotCommandResult::Retry => {
                                            call_api_without_user_interaction = true;
                                        }
                                    }
                                }
                            }
                        } else {
                            {
                                app_arc
                                    .lock()
                                    .unwrap()
                                    .add_message(ChatMessage::user().content(success).build());
                            } // `app` (MutexGuard) is dropped here
                            let api_call_result =
                                handle_api_call_with_retries(&app_arc.clone()).await;
                            call_api_without_user_interaction = matches!(
                                api_call_result,
                                ApiCallResult::ContinueWithoutUserInteraction
                                    | ApiCallResult::ToolFailed
                            );
                            if !call_api_without_user_interaction {
                                notification_required_on_user_input = true;
                            }
                        }
                    }
                    ReadResult::ShouldQuit => {
                        break 'main_loop;
                    }
                    ReadResult::Retry => match handle_retry_command(&app_arc.clone()) {
                        DotCommandResult::Exit => break 'main_loop,
                        DotCommandResult::Continue => {
                            notification_required_on_user_input = true;
                        }
                        DotCommandResult::Retry => {
                            call_api_without_user_interaction = true;
                        }
                    },
                    ReadResult::ShowDotCommands => {
                        handle_commands_command(CommandContext {
                            input: ".",
                            session_id: &session_id,
                            app_arc: &app_arc,
                        });
                        notification_required_on_user_input = true;
                    }
                    ReadResult::ChangeModel => {
                        open_provider_picker(CommandContext {
                            input: ".model",
                            session_id: &session_id,
                            app_arc: &app_arc,
                        });
                        notification_required_on_user_input = true;
                    }
                }
            }
        }
    }
    Ok(())
}
