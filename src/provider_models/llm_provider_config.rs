use crate::provider_models::common_config::LlmProviderConfig;
use crate::provider_models::config::{
    cohere, github_copilot, google_gemini, kluster_ai, mistral_ai, openrouter, pollinations_ai,
    vertex_ai,
};
use serde::{Deserialize, Serialize};
use std::fmt::{Di<PERSON><PERSON>, Formatter};
use std::str::FromStr;

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize, enum_iterator::Sequence)]
#[allow(non_camel_case_types)]
pub enum LlmProviderType {
    /// GitHub Copilot Claude 3.5 Sonnet
    /// Their API reports that it only has 90k context, although the model supports 200k.
    GitHubCopilotClaudeSonnet3_5,

    /// 6,000,000 tokens/day
    /// 250,000 tokens/minute
    /// 100 requests/day
    /// 5 requests/minute
    GoogleGemini2_5Pro,
    /// 250,000 tokens/minute
    /// 250 requests/day
    /// 10 requests/minute
    GoogleGemini2_5Flash,
    /// 1,000,000 tokens/minute
    /// 200 requests/day
    /// 15 requests/minute
    GoogleGemini2_0Flash,

    /// https://platform.kluster.ai/models/681114f25f778cfa68a9b9fa
    /// Context window: Up to 32K tokens
    /// Max output: Up to 4K tokens
    KlusterAiFreeQwen3_235B_A22B_FP8,
    /// https://platform.kluster.ai/models/68375430947bc4074a7d3198
    /// Context window: Up to 32K tokens
    /// Max output: Up to 4K tokens
    KlusterAiFreeDeepSeek_R1_0528,

    /// https://console.cloud.google.com/vertex-ai/publishers/deepseek-ai/model-garden/deepseek-r1-0528-maas
    /// 60 requests/minute
    /// Free during preview
    VertexAiDeepSeek_R1_0528,

    /// https://platform.openai.com/docs/models/gpt-4.1
    PollinationsAiGpt4_1,
    /// https://www.prompthub.us/models/o3
    PollinationsAiGpt_o3,
    /// Note that https://text.pollinations.ai/models reports no tool use.
    PollinationsAiDeepSeekV3,
    /// Note that https://text.pollinations.ai/models reports no tool use.
    PollinationsAiDeepSeekR1_0528,
    /// https://docs.x.ai/docs/models
    PollinationsAiGrok3Mini,
    /// https://openrouter.ai/mistralai/mistral-small-3.1-24b-instruct:free
    PollinationsAiMistralSmall3_1_24b_instruct,

    /// https://openrouter.ai/mistralai/mistral-small-3.2-24b-instruct:free
    OpenRouterMistralSmall3_2_24BInstruct,

    /// https://docs.cohere.com/v2/docs/models
    CohereCommandA03_2025,
    /// https://docs.cohere.com/v2/docs/models
    CohereCommandR08_2024,

    /// https://mistral.ai/news/codestral-2501
    /// 30 requests/minute
    /// 2,000 requests/day
    MistralAiCodestral,
}

impl LlmProviderType {
    pub fn get_config(&self) -> LlmProviderConfig {
        match self {
            LlmProviderType::GitHubCopilotClaudeSonnet3_5 => {
                github_copilot::get_claude_3_5_sonnet_config()
            }
            LlmProviderType::GoogleGemini2_0Flash => google_gemini::get_gemini_2_0_flash_config(),
            LlmProviderType::GoogleGemini2_5Flash => google_gemini::get_gemini_2_5_flash_config(),
            LlmProviderType::GoogleGemini2_5Pro => google_gemini::get_gemini_2_5_pro_config(),
            LlmProviderType::KlusterAiFreeQwen3_235B_A22B_FP8 => {
                kluster_ai::get_qwen_3_235b_a22b_fp8_config()
            }
            LlmProviderType::KlusterAiFreeDeepSeek_R1_0528 => {
                kluster_ai::get_deepseek_r1_0528_config()
            }
            LlmProviderType::VertexAiDeepSeek_R1_0528 => vertex_ai::get_deepseek_r1_0528_config(),
            LlmProviderType::PollinationsAiGpt4_1 => pollinations_ai::get_gpt4_1_config(),
            LlmProviderType::PollinationsAiGpt_o3 => pollinations_ai::get_o3_config(),
            LlmProviderType::PollinationsAiDeepSeekV3 => pollinations_ai::get_deepseek_v3_config(),
            LlmProviderType::PollinationsAiDeepSeekR1_0528 => {
                pollinations_ai::get_deepseek_r1_0528_config()
            }
            LlmProviderType::PollinationsAiGrok3Mini => pollinations_ai::get_grok_3_mini_config(),
            LlmProviderType::PollinationsAiMistralSmall3_1_24b_instruct => {
                pollinations_ai::get_mistral_small_3_1_24b_instruct_config()
            }
            LlmProviderType::OpenRouterMistralSmall3_2_24BInstruct => {
                openrouter::get_mistral_small_3_2_24b_instruct_config()
            }
            LlmProviderType::CohereCommandA03_2025 => cohere::get_command_a_03_2025_config(),
            LlmProviderType::CohereCommandR08_2024 => cohere::get_command_r_08_2024_config(),
            LlmProviderType::MistralAiCodestral => mistral_ai::get_codestral_config(),
        }
    }

    pub fn is_gemini(&self) -> bool {
        matches!(
            self,
            LlmProviderType::GoogleGemini2_0Flash
                | LlmProviderType::GoogleGemini2_5Flash
                | LlmProviderType::GoogleGemini2_5Pro
        )
    }
}

impl Display for LlmProviderType {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.get_config().startup_banner_text)
    }
}

impl FromStr for LlmProviderType {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "GitHubCopilotClaudeSonnet3_5" => Ok(LlmProviderType::GitHubCopilotClaudeSonnet3_5),
            "GoogleGemini2_0Flash" => Ok(LlmProviderType::GoogleGemini2_0Flash),
            "GoogleGemini2_5Flash" => Ok(LlmProviderType::GoogleGemini2_5Flash),
            "GoogleGemini2_5Pro" => Ok(LlmProviderType::GoogleGemini2_5Pro),
            "QwenQwen3235BA22BFP8" => Ok(LlmProviderType::KlusterAiFreeQwen3_235B_A22B_FP8),
            "DeepSeekR1" => Ok(LlmProviderType::KlusterAiFreeDeepSeek_R1_0528),
            "VertexAiDeepSeekR1" => Ok(LlmProviderType::VertexAiDeepSeek_R1_0528),
            "OpenRouterMistralSmall3_2_24BInstruct" => {
                Ok(LlmProviderType::OpenRouterMistralSmall3_2_24BInstruct)
            }
            "CohereCommandA03_2025" => Ok(LlmProviderType::CohereCommandA03_2025),
            "CohereCommandR08_2024" => Ok(LlmProviderType::CohereCommandR08_2024),
            "MistralAiCodestral" => Ok(LlmProviderType::MistralAiCodestral),
            "PollinationsAiGpt4_1" => Ok(LlmProviderType::PollinationsAiGpt4_1),
            "PollinationsAiDeepSeekV3" => Ok(LlmProviderType::PollinationsAiDeepSeekV3),
            "PollinationsAiDeepSeekR10528" => Ok(LlmProviderType::PollinationsAiDeepSeekR1_0528),
            "PollinationsAiGrok3Mini" => Ok(LlmProviderType::PollinationsAiGrok3Mini),
            "PollinationsAiMistralSmall3_1_24b_instruct" => {
                Ok(LlmProviderType::PollinationsAiMistralSmall3_1_24b_instruct)
            }
            "PollinationsAiGpt_o3" => Ok(LlmProviderType::PollinationsAiGpt_o3),
            _ => Err(format!("Unknown LLM provider type: {}", s)),
        }
    }
}

impl LlmProviderType {
    /// This needs to match the startup banner text exactly in the LlmProviderConfig.
    pub fn from_startup_banner_text(s: &str) -> Result<Self, String> {
        match s {
            "GitHub Copilot: Claude 3.5 Sonnet" => {
                Ok(LlmProviderType::GitHubCopilotClaudeSonnet3_5)
            }
            "Google: Gemini 2.0 Flash" => Ok(LlmProviderType::GoogleGemini2_0Flash),
            "Google: Gemini 2.5 Flash" => Ok(LlmProviderType::GoogleGemini2_5Flash),
            "Google: Gemini 2.5 Pro" => Ok(LlmProviderType::GoogleGemini2_5Pro),
            "kluster.ai: Qwen3-235B-A22B-FP8" => {
                Ok(LlmProviderType::KlusterAiFreeQwen3_235B_A22B_FP8)
            }
            "kluster.ai: DeepSeek-R1-0528" => Ok(LlmProviderType::KlusterAiFreeDeepSeek_R1_0528),
            "Vertex AI: DeepSeek R1-0528" => Ok(LlmProviderType::VertexAiDeepSeek_R1_0528),
            "Pollinations.ai: GPT-4.1" => Ok(LlmProviderType::PollinationsAiGpt4_1),
            "PollinationsAiGpt41" => Ok(LlmProviderType::PollinationsAiGpt4_1),
            "Pollinations.ai: DeepSeek V3" => Ok(LlmProviderType::PollinationsAiDeepSeekV3),
            "Pollinations.ai: GPT-o3" => Ok(LlmProviderType::PollinationsAiGpt_o3),
            "OpenRouter: Mistral Small 3.2 24B" => {
                Ok(LlmProviderType::OpenRouterMistralSmall3_2_24BInstruct)
            }
            "Cohere: Command A-03 2025" => Ok(LlmProviderType::CohereCommandA03_2025),
            "Cohere: Command R-08 2024" => Ok(LlmProviderType::CohereCommandR08_2024),
            "Mistral AI: Codestral" => Ok(LlmProviderType::MistralAiCodestral),
            "Pollinations.ai: Grok-3-Mini" => Ok(LlmProviderType::PollinationsAiGrok3Mini),
            "Pollinations.ai: Mistral Small 3.1 24B" => {
                Ok(LlmProviderType::PollinationsAiMistralSmall3_1_24b_instruct)
            }
            _ => Err(format!("Unknown LLM provider type: {}", s)),
        }
    }
}
