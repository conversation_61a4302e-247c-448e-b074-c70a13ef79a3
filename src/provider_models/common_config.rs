use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub struct LlmProviderConfig {
    pub context_window_size: usize,
    pub api_model_name: String,
    pub startup_banner_text: String,
    pub backend: SerializableLLMBackend,
    /// This is not set for Google Gemini models.
    pub base_url: Option<String>,
    /// Optional for GitHub Copilot.
    pub api_key_env_name: Option<String>,
    pub retry_on_api_error: bool,
    pub retry_info_display: RetryInfoDisplay,
    /// Needs special handling.
    pub is_github_copilot: bool,
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum RetryInfoDisplay {
    ShowFull,
    ShowShort,
    Hide,
}
