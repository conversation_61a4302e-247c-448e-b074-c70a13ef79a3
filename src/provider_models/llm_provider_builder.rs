use crate::cli::cli_args::Cli;
use crate::constants::NETWORK_TIMEOUT_SECONDS;
use crate::display::print::print_error;
use crate::provider_models::common_config::LlmProviderConfig;
use crate::tools::utils::llm_provider_tool_setup::setup_tools;
use llm::builder::LLMBuilder;
use llm::LLMProvider;
use std::env::var;
use crate::session_management::session_dir::get_session_dir;

pub fn build_llm_provider(
    system_prompt: String,
    proxy_url: Option<String>,
    model_config: LlmProviderConfig,
    cli_args: &Cli,
) -> Box<dyn LLMProvider> {
    let mut builder = LLMBuilder::new()
        .backend(model_config.backend.into())
        .model(&model_config.api_model_name)
        .system(system_prompt)
        .temperature(0.7)
        .timeout_seconds(NETWORK_TIMEOUT_SECONDS);
    if model_config.is_github_copilot {
        builder = builder.github_copilot_token_directory(get_session_dir());
    }
    let env_name = &model_config.api_key_env_name;
    match env_name {
        None => {}
        Some(env_name) => match var(env_name) {
            Ok(key) => {
                builder = builder.api_key(key);
            }
            Err(_) => {
                print_error(format!(
                    "Environment API key {} is not set!",
                    model_config.api_key_env_name.as_ref().unwrap()
                ));
            }
        },
    };
    if let Some(base_url) = model_config.base_url {
        builder = builder.base_url(&base_url);
    }
    if !cli_args.aider {
        builder = setup_tools(builder, cli_args);
    }
    if let Some(proxy_url) = proxy_url {
        builder = builder.proxy_url(proxy_url);
    }
    builder.build().expect("Failed to build LLM")
}