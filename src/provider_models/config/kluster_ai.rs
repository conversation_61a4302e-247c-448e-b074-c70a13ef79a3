use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_qwen_3_235b_a22b_fp8_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 32_000,
        api_model_name: "Qwen/Qwen3-235B-A22B-FP8".to_string(),
        startup_banner_text: "kluster.ai: Qwen3-235B-A22B-FP8".to_string(),
        api_key_env_name: Some("KLUSTER_API_KEY".to_string()),
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://api.kluster.ai/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
    }
}

pub fn get_deepseek_r1_0528_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 32_000,
        api_model_name: "deepseek-ai/DeepSeek-R1-0528".to_string(),
        startup_banner_text: "kluster.ai: DeepSeek-R1-0528".to_string(),
        api_key_env_name: Some("KLUSTER_API_KEY".to_string()),
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://api.kluster.ai/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
    }
}
