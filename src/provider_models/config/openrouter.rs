use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_mistral_small_3_2_24b_instruct_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 96_000,
        api_model_name: "mistralai/mistral-small-3.2-24b-instruct:free".to_string(),
        startup_banner_text: "OpenRouter: Mistral Small 3.2 24B".to_string(),
        api_key_env_name: Some("OPENROUTER_API_KEY".to_string()),
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://openrouter.ai/api/v1/".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
    }
}
