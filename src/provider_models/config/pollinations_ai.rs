use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_gpt4_1_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 1_047_576,
        api_model_name: "gpt-4.1".to_string(),
        startup_banner_text: "Pollinations.ai: GPT-4.1".to_string(),
        api_key_env_name: Some("POLLINATIONS_AI_API_KEY".to_string()),
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai/v1/".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
    }
}

pub fn get_o3_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 200_000,
        api_model_name: "o3".to_string(),
        startup_banner_text: "Pollinations.ai: GPT-o3".to_string(),
        api_key_env_name: Some("POLLINATIONS_AI_API_KEY".to_string()),
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai/v1/".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
    }
}

pub fn get_deepseek_v3_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 128_000,
        api_model_name: "deepseek-v3".to_string(),
        startup_banner_text: "Pollinations.ai: DeepSeek V3".to_string(),
        api_key_env_name: Some("POLLINATIONS_AI_API_KEY".to_string()),
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai/v1/".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
    }
}

pub fn get_deepseek_r1_0528_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 128_000,
        api_model_name: "deepseek-ai/deepseek-r1-0528-maas".to_string(),
        startup_banner_text: "Pollinations.ai: DeepSeek R1-0528".to_string(),
        api_key_env_name: Some("POLLINATIONS_AI_API_KEY".to_string()),
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai/v1/".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
    }
}

pub fn get_grok_3_mini_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 131_072,
        api_model_name: "grok-3-mini".to_string(),
        startup_banner_text: "Pollinations.ai: Grok-3-Mini".to_string(),
        api_key_env_name: Some("POLLINATIONS_AI_API_KEY".to_string()),
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai/v1/".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
    }
}

pub fn get_mistral_small_3_1_24b_instruct_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 96_000,
        api_model_name: "mistral-small-3.1-24b-instruct".to_string(),
        startup_banner_text: "Pollinations.ai: Mistral Small 3.1 24B".to_string(),
        api_key_env_name: Some("POLLINATIONS_AI_API_KEY".to_string()),
        backend: SerializableLLMBackend::OpenAI,
        base_url: Some("https://text.pollinations.ai/openai/v1/".to_string()),
        retry_on_api_error: true,
        retry_info_display: RetryInfoDisplay::ShowShort,
        is_github_copilot: false,
    }
}
