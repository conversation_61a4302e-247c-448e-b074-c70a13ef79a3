use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_command_a_03_2025_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 256_000,
        api_model_name: "command-a-03-2025".to_string(),
        startup_banner_text: "Cohere: Command A-03 2025".to_string(),
        api_key_env_name: Some("COHERE_API_KEY".to_string()),
        backend: SerializableLLMBackend::Cohere,
        base_url: Some("https://api.cohere.ai/v1".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
    }
}

pub fn get_command_r_08_2024_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 128_000,
        api_model_name: "command-r-08-2024".to_string(),
        startup_banner_text: "Cohere: Command R-08 2024".to_string(),
        api_key_env_name: Some("COHERE_API_KEY".to_string()),
        backend: SerializableLLMBackend::Cohere,
        base_url: Some("https://api.cohere.ai/v1".to_string()),
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
    }
}
