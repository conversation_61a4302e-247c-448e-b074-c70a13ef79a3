use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_gemini_2_0_flash_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 1_000_000,
        api_model_name: "gemini-2.0-flash".to_string(),
        startup_banner_text: "Google: Gemini 2.0 Flash".to_string(),
        api_key_env_name: Some("GOOGLE_GEMINI_API_KEY".to_string()),
        backend: SerializableLLMBackend::Google,
        base_url: None,
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
    }
}

pub fn get_gemini_2_5_flash_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 1_000_000,
        api_model_name: "gemini-2.5-flash".to_string(),
        startup_banner_text: "Google: Gemini 2.5 Flash".to_string(),
        api_key_env_name: Some("GOOGLE_GEMINI_API_KEY".to_string()),
        backend: SerializableLLMBackend::Google,
        base_url: None,
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
    }
}

pub fn get_gemini_2_5_pro_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 1_000_000,
        api_model_name: "gemini-2.5-pro".to_string(),
        startup_banner_text: "Google: Gemini 2.5 Pro".to_string(),
        api_key_env_name: Some("GOOGLE_GEMINI_API_KEY".to_string()),
        backend: SerializableLLMBackend::Google,
        base_url: None,
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: false,
    }
}
