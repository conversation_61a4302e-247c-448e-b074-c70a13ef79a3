use crate::provider_models::common_config::{LlmProviderConfig, RetryInfoDisplay};
use crate::provider_models::serializable_llm_backend::SerializableLLMBackend;

pub fn get_claude_3_5_sonnet_config() -> LlmProviderConfig {
    LlmProviderConfig {
        context_window_size: 90_000,
        api_model_name: "claude-3.5-sonnet".to_string(),
        startup_banner_text: "GitHub Copilot: Claude 3.5 Sonnet".to_string(),
        api_key_env_name: None,
        base_url: None,
        backend: SerializableLLMBackend::GitHubCopilot,
        retry_on_api_error: false,
        retry_info_display: RetryInfoDisplay::ShowFull,
        is_github_copilot: true,
    }
}
