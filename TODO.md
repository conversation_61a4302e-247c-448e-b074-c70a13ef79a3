# add error to write_file_tool.rs when the new file content is the same as the old

also add one check if only the trailing newline was changed/removed, error also on that if the content is otherwise the same

# do not trigger notification on ctrl-c request abortions

and not on changing model or on executing any dot commands

# maybe still consider streaming?

but mostly just to indicate tool calls, sometimes one is waiting long just because it writes the whole file?

# try out gem<PERSON><PERSON>' system prompt, maybe guard behind --autonomous system prompt

the current one needs heavy user interaction, if not explicity told to, which is really nice sometimes!