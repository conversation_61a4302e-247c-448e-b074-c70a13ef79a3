# GitHub Copilot

The free tier has 500 free requests across many models, including Claude 3.5 Sonnet.

You will be able to use a command to relogin to switch to a different account for more access.

## Claude 3.5 Sonnet

It is pretty slow to respond.

# Google

Requires the `GOOGLE_GEMINI_API_KEY` environment variable to be set.

The Gemini 2.5 and 2.0 Flash models are pretty good, they are a bit annoying since they ask back a lot.

## Gemini 2.5 Flash (✨ excellent)

## Gemini 2.0 Flash (✅ fine)

Way worse than Gemini 2.5 Flash actually, but still usable.

# Mistral Codestral (✅ okay)

With `OnlyWriteFile`, it is alright, but not as good as the Google Gemini models.

# Cohere

## Command R-08 2024

Can fail with 422 with this error which goes away on retrying.

```json
{
  "id": "********-5921-4b6e-bc84-9c1aaa7f0674",
  "message": "your request resulted in an invalid tool generation. Try updating the messages or tool definitions"
}
```

It keeps getting 'stuck' by writing `I will now ...`, but forgets to call tools, so the loop is broken.

# OpenRouter

## deepseek/deepseek-r1-0528:free, qwen3-235b-a22b:free

Do not support tool use, so it is not included.

## Mistral 3.1 24B Instruct / Mistral 3.2 24B Instruct

Just broken, always fail with this, although 200 it returns a status code.

Other free models like `deepseek/deepseek-chat:free` work just fine, although they have no tool support.

```json
{
  "error": {
    "message": "Internal Server Error",
    "code": 500
  },
  "user_id": "user_2towirRZERkEs9tld9TtQCTZNHb"
}
```

# Pollinations.AI

Often has network issues which go away on retrying, sometimes on retrying a lot.

On requesting o3, DeepSeek, grok or GPT-4.1 models, very often, the model is instead just `gpt-4.1-nano-2025-04-14`.

## Mistral 3.1 24B Instruct

Is just incapable of using the `find_and_replace_code` tool. It almost always messes it up.

## GPT-4.1

Can perform really well, but not always.
It is really nice when it just needs to run some bash commands.

It often messes up the `find_and_replace_code` tool and does not even understand it, when the tool reports failure.

# Google Vertex.AI

Very often, the token behind `VERTEX_AI_AUTHORIZATION` expires and needs to be refreshed.

You need to run `gcloud auth print-access-token`. I do it in the Cloud Shell.

## Deepseek-R1-0528

As soon as any tool responses are used in the request body, it fails with `500: Internal error encountered.`,
rendering this useless without `--aider`.

Also, the response body does not differentiate between `content` and `reasoning_content`.
The chain of thought (COT) is not supposed to be appended to the assistant messages, but it is impossible to distinguish
between the two reliably because the COT is not in a separate message.

# Kluster.ai

https://platform.kluster.ai/plans

Has those free limits making it impractical because the context window is too small,
even if the model is good.

- Requests per minute: 30
- Context window: Up to 32K tokens
- Max output: Up to 4K tokens

# Inference.net

https://inference.net/

Free credits: $1, $25 on responding to an email survey

Does not have any interesting models for this use-case.